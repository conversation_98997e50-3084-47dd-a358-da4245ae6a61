// Webhook utility functions for N8N integration

export interface WebhookResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface ModelData {
  id: number
  name: string
  model: string
  provider: string
  key: string
  input_cost_per_token: number
  output_cost_per_token: number
  cache_read_cost_per_token: number
  cache_write_cost_per_token: number
  thinking_cost_per_token: number
  audio_input_cost_per_token: number
  updated_at: string
}

export interface RequestLogData {
  id: string
  conversation_id: string
  client_id: string
  customer_id: string
  platform: string
  operation: string
  task: string
  model_code: string
  model: string
  provider: string
  input_tokens: number
  output_tokens: number
  cache_read_tokens: number
  cache_write_tokens: number
  thinking_tokens: number
  audio_input_tokens: number
  input_cost: number
  output_cost: number
  cache_read_cost: number
  cache_write_cost: number
  thinking_cost: number
  audio_input_cost: number
  total_cost: number
  flag: string
  created_at: string
}

export interface UserActionData {
  action: 'upgrade_plan' | 'add_messages' | 'reset_password' | 'clear_data' | 'add_user'
  user_id?: string
  email?: string
  plan?: string
  message_count?: number
  additional_data?: Record<string, any>
}

export interface AnalyticsData {
  total_requests: number
  total_cost: number
  total_tokens: number
  average_cost_per_request: number
  daily_requests: Array<{ date: string; count: number }>
  daily_costs: Array<{ date: string; cost: number }>
  recent_logs: RequestLogData[]
}

// Generic webhook caller
async function callWebhook<T>(
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  data?: any
): Promise<WebhookResponse<T>> {
  try {
    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    }

    if (data && method !== 'GET') {
      options.body = JSON.stringify(data)
    }

    const response = await fetch(url, options)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    
    return {
      success: true,
      data: result,
    }
  } catch (error) {
    console.error('Webhook call failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}

// Model Management Webhooks
export const modelWebhooks = {
  // Fetch all models
  fetchModels: async (): Promise<WebhookResponse<ModelData[]>> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    return callWebhook<ModelData[]>(url, 'POST', {
      operation: 'model',
      method: 'get'
    })
  },

  // Add new model
  addModel: async (modelData: Omit<ModelData, 'id' | 'updated_at'>): Promise<WebhookResponse> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    return callWebhook(url, 'POST', {
      operation: 'model',
      method: 'add',
      ...modelData
    })
  },

  // Delete model
  deleteModel: async (modelId: number): Promise<WebhookResponse> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    return callWebhook(url, 'POST', {
      operation: 'model',
      method: 'delete',
      id: modelId
    })
  },

  // Update model
  updateModel: async (modelId: number, modelData: Partial<ModelData>): Promise<WebhookResponse> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    return callWebhook(url, 'POST', {
      operation: 'model',
      method: 'update',
      id: modelId,
      ...modelData
    })
  },
}

// Analytics Webhooks
export const analyticsWebhooks = {
  // Fetch analytics overview
  fetchOverview: async (
    startDate?: string,
    endDate?: string
  ): Promise<WebhookResponse<AnalyticsData>> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    
    // Prepare request body with operation and parameters
    const requestBody: Record<string, any> = {
      operation: 'request-log',
      mode: 'analytic'
    }
    
    if (startDate) requestBody.start_date = startDate
    if (endDate) requestBody.end_date = endDate
    
    return callWebhook<AnalyticsData>(url, 'POST', requestBody)
  },

  // Fetch request logs with pagination
  fetchRequestLogs: async (
    page: number = 1,
    rows: number = 50,
    filters?: Record<string, any>
  ): Promise<WebhookResponse<RequestLogData[] | { data: RequestLogData[]; total: number; page: number; total_pages: number; limit: number }>> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    
    // Prepare request body with operation and parameters
    const requestBody: Record<string, any> = {
      operation: 'request-log',
      page,
      rows,
      // Default mode is 'logs', but can be overridden by filters
      mode: filters?.mode || 'logs'
    }
    
    // Add filters to request body
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && key !== 'mode') {
          requestBody[key] = value
        }
      })
    }
    
    return callWebhook(url, 'POST', requestBody)
  },

  // Fetch daily metrics
  fetchDailyMetrics: async (
    startDate: string,
    endDate: string
  ): Promise<WebhookResponse<{ requests: Array<{ date: string; count: number }>; costs: Array<{ date: string; cost: number }> }>> => {
    const url = new URL(`${process.env.REQUEST_LOG_WEBHOOK_URL!}/daily`)
    url.searchParams.append('start_date', startDate)
    url.searchParams.append('end_date', endDate)
    
    return callWebhook(url.toString(), 'GET')
  },

}

// User Management Webhooks
export const userWebhooks = {
  // Send plan update notification to webhook
  updatePlanNotification: async (notificationData: { 
    mode: string; 
    client_id: string; 
    plan: string; 
    usage_limit: number 
  }): Promise<WebhookResponse> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    
    return callWebhook(url, 'POST', {
      operation: 'user',
      mode: notificationData.mode,
      client_id: notificationData.client_id,
      plan: notificationData.plan,
      usage_limit: notificationData.usage_limit,
    })
  },

  // Send add message notification to webhook
  addMessageNotification: async (notificationData: {
    mode: string;
    client_id: string;
    usage_limit: number;
  }): Promise<WebhookResponse> => {
    const url = process.env.ADMIN_DASHBOARD_WEBHOOK_URL!
    
    return callWebhook(url, 'POST', {
      operation: 'user',
      mode: notificationData.mode,
      client_id: notificationData.client_id,
      usage_limit: notificationData.usage_limit,
    })
  },

}

// Cache utility for reducing webhook calls
export class WebhookCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  set(key: string, data: any, ttlMinutes: number = 5) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMinutes * 60 * 1000,
    })
  }

  get(key: string) {
    const cached = this.cache.get(key)
    if (!cached) return null

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }

    return cached.data
  }

  clear() {
    this.cache.clear()
  }
}

export const webhookCache = new WebhookCache()