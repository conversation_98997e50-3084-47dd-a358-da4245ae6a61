// Supabase admin operations for direct database access
import { createServiceRoleClient } from '@/lib/supabase'
import { calculateNextBillingDate, getUsageLimitForPlan, getCurrentPhnomPenhTime } from '@/lib/billing-utils'

export interface ClientData {
  auth_id: string
  client_id: string
  username: string
  plan_type: string | null
  billing_cycle: string | null
  usage_limit: number | null
  next_billing_date: string | null
  start_date: string | null
}

export interface UpdatePlanData {
  planType: string
  billingCycle: string
  usageLimit: number
  startDate: string
  nextBillingDate: string
}

export async function findClientByUsername(username: string): Promise<ClientData | null> {
  const supabase = createServiceRoleClient()
  
  try {
    const { data, error } = await supabase
      .from('clients')
      .select('auth_id, client_id, username, plan_type, billing_cycle, usage_limit, next_billing_date, start_date')
      .eq('username', username)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null
      }
      throw error
    }

    return data
  } catch (error) {
    console.error('Error finding client by username:', error)
    throw new Error('Failed to find client')
  }
}

export async function updateClientPlan(
  clientId: string, 
  planType: string, 
  billingCycle: string
): Promise<ClientData> {
  const supabase = createServiceRoleClient()
  
  try {
    const usageLimit = getUsageLimitForPlan(planType)
    const startDate = new Date().toISOString() // UTC timestamp
    const nextBillingDate = calculateNextBillingDate(billingCycle)

    const { data, error } = await supabase
      .from('clients')
      .update({
        plan_type: planType,
        billing_cycle: billingCycle,
        usage_limit: usageLimit,
        start_date: startDate,
        next_billing_date: nextBillingDate,
        updated_at: new Date().toISOString() // UTC timestamp
      })
      .eq('client_id', clientId)
      .select('auth_id, client_id, username, plan_type, billing_cycle, usage_limit, next_billing_date, start_date')
      .single()

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    console.error('Error updating client plan:', error)
    throw new Error('Failed to update client plan')
  }
}

export async function insertClientSubscription(
  clientId: string,
  planType: string,
  billingCycle: string,
  startDate: string,
  nextBillingDate: string
): Promise<void> {
  const supabase = createServiceRoleClient()
  
  try {
    const { error } = await supabase
      .from('client_subscriptions')
      .insert({
        client_id: clientId,
        plan_type: planType,
        billing_cycle: billingCycle,
        start_date: startDate,
        next_billing_date: nextBillingDate,
        created_at: new Date().toISOString(), // UTC timestamp
        updated_at: new Date().toISOString()  // UTC timestamp
      })

    if (error) {
      throw error
    }
  } catch (error) {
    console.error('Error inserting client subscription:', error)
    throw new Error('Failed to create subscription record')
  }
}

export async function updateUserPlan(
  username: string,
  planType: string,
  billingCycle: string
): Promise<{
  success: boolean
  data?: {
    username: string
    plan: string
    next_billing_date: string
    client_id: string
    usage_limit: number
  }
  error?: string
}> {
  try {
    // Step 1: Find client by username
    const client = await findClientByUsername(username)
    
    if (!client) {
      return {
        success: false,
        error: 'No user found with that username'
      }
    }

    // Step 2: Update clients table
    const updatedClient = await updateClientPlan(client.client_id, planType, billingCycle)

    // Step 3: Insert into client_subscriptions table
    await insertClientSubscription(
      client.client_id,
      planType,
      billingCycle,
      updatedClient.start_date!,
      updatedClient.next_billing_date!
    )

    return {
      success: true,
      data: {
        username: updatedClient.username,
        plan: updatedClient.plan_type!,
        next_billing_date: updatedClient.next_billing_date!,
        client_id: updatedClient.client_id,
        usage_limit: updatedClient.usage_limit!
      }
    }
  } catch (error) {
    console.error('Error in updateUserPlan:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function findClientByUsernameForMessages(username: string): Promise<{
  client_id: string
  username: string
  usage_limit: number
} | null> {
  const supabase = createServiceRoleClient()
  
  try {
    const { data, error } = await supabase
      .from('clients')
      .select('client_id, username, usage_limit')
      .eq('username', username)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null
      }
      throw error
    }

    return data
  } catch (error) {
    console.error('Error finding client by username for messages:', error)
    throw new Error('Failed to find client')
  }
}

export async function updateClientUsageLimit(
  clientId: string,
  newUsageLimit: number
): Promise<{
  client_id: string
  username: string
  usage_limit: number
}> {
  const supabase = createServiceRoleClient()
  
  try {
    const { data, error } = await supabase
      .from('clients')
      .update({
        usage_limit: newUsageLimit,
        updated_at: new Date().toISOString()
      })
      .eq('client_id', clientId)
      .select('client_id, username, usage_limit')
      .single()

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    console.error('Error updating client usage limit:', error)
    throw new Error('Failed to update usage limit')
  }
}

export async function addUserMessages(
  username: string,
  messageCount: number
): Promise<{
  success: boolean
  data?: {
    username: string
    client_id: string
    previous_usage_limit: number
    new_usage_limit: number
    messages_added: number
  }
  error?: string
}> {
  try {
    // Step 1: Find client by username
    const client = await findClientByUsernameForMessages(username)
    
    if (!client) {
      return {
        success: false,
        error: 'No user found with that username'
      }
    }

    // Step 2: Calculate new usage limit
    const currentUsageLimit = client.usage_limit || 0
    const newUsageLimit = currentUsageLimit + messageCount

    // Step 3: Update clients table
    const updatedClient = await updateClientUsageLimit(client.client_id, newUsageLimit)

    return {
      success: true,
      data: {
        username: updatedClient.username,
        client_id: updatedClient.client_id,
        previous_usage_limit: currentUsageLimit,
        new_usage_limit: updatedClient.usage_limit,
        messages_added: messageCount
      }
    }
  } catch (error) {
    console.error('Error in addUserMessages:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}