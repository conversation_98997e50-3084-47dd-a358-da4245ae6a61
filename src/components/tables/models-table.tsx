'use client'

import { useState, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ModelForm } from '@/components/forms/model-form'
import { ModelData } from '@/types'
import { Edit, ChevronLeft, ChevronRight } from 'lucide-react'
import { toast } from 'sonner'

interface ModelsTableProps {
  models: ModelData[]
  onDelete: (modelId: number) => void
  onUpdate: (modelId: number, data: Partial<ModelData>) => void
  loading?: boolean
}

export function ModelsTable({ models, onDelete, onUpdate, loading }: ModelsTableProps) {
  const [editingModel, setEditingModel] = useState<ModelData | null>(null)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 25

  // Calculate pagination
  const { paginatedModels, totalPages, startIndex, endIndex } = useMemo(() => {
    const start = (currentPage - 1) * itemsPerPage
    const end = start + itemsPerPage
    const paginated = models.slice(start, end)
    const pages = Math.ceil(models.length / itemsPerPage)
    
    return {
      paginatedModels: paginated,
      totalPages: pages,
      startIndex: start + 1,
      endIndex: Math.min(end, models.length)
    }
  }, [models, currentPage, itemsPerPage])

  // Reset to first page when models change
  useMemo(() => {
    setCurrentPage(1)
  }, [models])

  const handleUpdate = async (data: Omit<ModelData, 'updated_at'>) => {
    if (!editingModel) return

    setUpdateLoading(true)
    try {
      await onUpdate(editingModel.id, data)
      setEditingModel(null)
    } catch {
      toast.error('Failed to update model')
    } finally {
      setUpdateLoading(false)
    }
  }

  const handleDelete = async (modelId: number) => {
    setDeleteLoading(true)
    try {
      await onDelete(modelId)
      setEditingModel(null)
    } catch {
      toast.error('Failed to delete model')
    } finally {
      setDeleteLoading(false)
    }
  }


  const formatCost = (cost: number | undefined) => {
    if (cost === undefined || cost === null) return '$0.00'
    const perMillionTokens = Number(cost) * 1000000
    return `$${perMillionTokens.toFixed(2)}`
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    )
  }

  if (models.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No models found. Add your first model to get started.
      </div>
    )
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Model</TableHead>
            <TableHead>Provider</TableHead>
            <TableHead>Input Cost</TableHead>
            <TableHead>Output Cost</TableHead>
            <TableHead>Updated</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {paginatedModels.map((model) => (
            <TableRow key={model.id}>
              <TableCell className="font-medium">{model.name}</TableCell>
              <TableCell>
                <Badge variant="outline">{model.model}</Badge>
              </TableCell>
              <TableCell>
                <Badge variant="secondary">{model.provider}</Badge>
              </TableCell>
              <TableCell>{formatCost(model.input_cost_per_token)}</TableCell>
              <TableCell>{formatCost(model.output_cost_per_token)}</TableCell>
              <TableCell>
                {new Date(model.updated_at).toLocaleDateString()}
              </TableCell>
              <TableCell>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingModel(model)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between space-x-6 lg:space-x-8 pt-4">
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            {models.length === 0 ? (
              "0 of 0"
            ) : (
              `${startIndex}-${endIndex} of ${models.length}`
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setCurrentPage(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => setCurrentPage(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      <Dialog open={!!editingModel} onOpenChange={(open) => !open && setEditingModel(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Model</DialogTitle>
          </DialogHeader>
          {editingModel && (
            <ModelForm
              onSubmit={handleUpdate}
              loading={updateLoading}
              initialData={editingModel}
              onDelete={handleDelete}
              deleteLoading={deleteLoading}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}