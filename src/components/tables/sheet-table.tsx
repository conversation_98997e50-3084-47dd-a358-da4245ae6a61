'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  <PERSON><PERSON>, 
  Arrow<PERSON>pDown, 
  ArrowUp, 
  ArrowDown, 
  Key, 
  Calendar, 
  Hash,
  Type,
  Database,
  CheckCircle,
  XCircle,
  ExternalLink,
  Mail,
  Phone
} from 'lucide-react'
import { toast } from 'sonner'
import { formatTimestamp, TABLE_SCHEMA_CONFIG, type ColumnSchema } from '@/lib/database'

interface SheetTableProps {
  data: {
    rows: Record<string, any>[]
    columns: string[]
    totalRows: number
  }
  tableName: string
  primaryKey?: string
}

// Get column schema from PostgreSQL definitions
const getColumnSchema = (tableName: string, columnName: string): ColumnSchema | null => {
  const tableConfig = TABLE_SCHEMA_CONFIG[tableName]
  if (!tableConfig) return null
  return tableConfig.columns[columnName] || null
}

// Detect data presentation type based on schema and value
const detectPresentationType = (value: any, columnName: string, schema: ColumnSchema | null): string => {
  if (!schema) return 'text'
  
  // Use schema type as base
  const schemaType = schema.type.toLowerCase()
  
  // Email detection from value
  if (typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    return 'email'
  }
  
  // Phone detection from value
  if (typeof value === 'string' && /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, ''))) {
    return 'phone'
  }
  
  // URL detection from value
  if (typeof value === 'string' && /^https?:\/\//.test(value)) {
    return 'url'
  }
  
  // Map PostgreSQL types to presentation types
  if (schemaType.includes('uuid')) return 'uuid'
  if (schemaType.includes('timestamp') || schemaType.includes('time')) return 'timestamp'
  if (schemaType.includes('boolean')) return 'boolean'
  if (schemaType.includes('json')) return 'jsonb'
  if (schemaType.includes('integer') || schemaType.includes('serial') || schemaType.includes('real')) return 'number'
  if (schemaType.includes('vector')) return 'vector'
  
  return 'text'
}

const getColumnIcon = (schema: ColumnSchema | null, isPrimary?: boolean) => {
  if (isPrimary) return <Key className="h-3 w-3 text-amber-500" />
  
  if (!schema) return <Type className="h-3 w-3 text-muted-foreground" />
  
  const schemaType = schema.type.toLowerCase()
  
  if (schemaType.includes('uuid')) return <Hash className="h-3 w-3 text-blue-500" />
  if (schemaType.includes('timestamp') || schemaType.includes('time')) return <Calendar className="h-3 w-3 text-purple-500" />
  if (schemaType.includes('integer') || schemaType.includes('serial') || schemaType.includes('real')) return <Hash className="h-3 w-3 text-green-500" />
  if (schemaType.includes('boolean')) return <CheckCircle className="h-3 w-3 text-orange-500" />
  if (schemaType.includes('json')) return <Database className="h-3 w-3 text-indigo-500" />
  if (schemaType.includes('vector')) return <Database className="h-3 w-3 text-pink-500" />
  
  return <Type className="h-3 w-3 text-muted-foreground" />
}

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text)
  toast.success('Copied to clipboard')
}

export function SheetTable({ data, tableName, primaryKey }: SheetTableProps) {
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set())

  // Get column schemas and presentation types
  const columnInfo = data.columns.reduce((acc, column) => {
    const schema = getColumnSchema(tableName, column)
    const sampleValue = data.rows.find(row => row[column] !== null && row[column] !== undefined)?.[column]
    const presentationType = detectPresentationType(sampleValue, column, schema)
    acc[column] = { schema, presentationType }
    return acc
  }, {} as Record<string, { schema: ColumnSchema | null, presentationType: string }>)

  const formatCellValue = (value: any, presentationType: string): string => {
    if (value === null || value === undefined) return ''
    
    switch (presentationType) {
      case 'timestamp':
        return formatTimestamp(value)
      case 'jsonb':
        return JSON.stringify(value)
      case 'boolean':
        return value ? 'true' : 'false'
      case 'uuid':
        return value.toString().substring(0, 8) + '...'
      case 'vector':
        return '[vector data]'
      default:
        const str = String(value)
        return str.length > 50 ? str.substring(0, 50) + '...' : str
    }
  }

  const renderCell = (value: any, dataType: DataType, fullValue: string) => {
    if (value === null || value === undefined) {
      return (
        <span className="text-muted-foreground italic text-sm">
          NULL
        </span>
      )
    }

    const cellContent = (() => {
      switch (dataType) {
        case 'uuid':
          return (
            <span className="font-mono text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">
              {formatCellValue(value, dataType)}
            </span>
          )
        case 'timestamp':
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="text-purple-600 cursor-help">
                    {formatCellValue(value, dataType)}
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="font-mono text-xs">{value}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )
        case 'boolean':
          return (
            <Badge variant={value ? 'default' : 'secondary'} className="text-xs">
              {value ? (
                <><CheckCircle className="h-3 w-3 mr-1" />true</>
              ) : (
                <><XCircle className="h-3 w-3 mr-1" />false</>
              )}
            </Badge>
          )
        case 'json':
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="font-mono text-sm text-indigo-600 bg-indigo-50 px-2 py-1 rounded cursor-help">
                    {formatCellValue(value, dataType)}
                  </span>
                </TooltipTrigger>
                <TooltipContent className="max-w-md">
                  <pre className="text-xs whitespace-pre-wrap">
                    {JSON.stringify(value, null, 2)}
                  </pre>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )
        case 'email':
          return (
            <a 
              href={`mailto:${value}`}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              {formatCellValue(value, dataType)}
            </a>
          )
        case 'phone':
          return (
            <a 
              href={`tel:${value}`}
              className="text-green-600 hover:text-green-800 underline"
            >
              {formatCellValue(value, dataType)}
            </a>
          )
        case 'url':
          return (
            <a 
              href={value}
              target="_blank"
              rel="noopener noreferrer"
              className="text-purple-600 hover:text-purple-800 underline inline-flex items-center gap-1"
            >
              {formatCellValue(value, dataType)}
              <ExternalLink className="h-3 w-3" />
            </a>
          )
        default:
          return (
            <span className="text-sm">
              {formatCellValue(value, dataType)}
            </span>
          )
      }
    })()

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div 
              className="group cursor-pointer hover:bg-muted/50 p-1 rounded relative"
              onClick={() => copyToClipboard(fullValue)}
            >
              {cellContent}
              <Copy className="h-3 w-3 absolute top-1 right-1 opacity-0 group-hover:opacity-50 transition-opacity" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-xs">Click to copy: {fullValue}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  const handleSort = (columnKey: string) => {
    let direction: 'asc' | 'desc' = 'asc'
    if (sortConfig && sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc'
    }
    setSortConfig({ key: columnKey, direction })
  }

  const getSortIcon = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return <ArrowUpDown className="h-3 w-3 opacity-50" />
    }
    return sortConfig.direction === 'asc' ? 
      <ArrowUp className="h-3 w-3 text-blue-500" /> : 
      <ArrowDown className="h-3 w-3 text-blue-500" />
  }

  return (
    <div className="border rounded-lg overflow-hidden bg-white">
      {/* Table Header */}
      <div className="bg-gray-50 border-b px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="font-semibold text-gray-900">{tableName}</h3>
          <Badge variant="outline" className="text-xs">
            {data.totalRows} rows
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            Export
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50/50 sticky top-0 z-10">
            <tr>
              <th className="w-12 p-3 text-left">
                <input 
                  type="checkbox" 
                  className="rounded border-gray-300"
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedRows(new Set(data.rows.map((_, i) => i)))
                    } else {
                      setSelectedRows(new Set())
                    }
                  }}
                />
              </th>
              {data.columns.map((column) => (
                <th 
                  key={column} 
                  className="px-3 py-4 text-left border-r border-gray-200 last:border-r-0 min-w-[120px]"
                >
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                      {getColumnIcon(columnTypes[column], column === primaryKey)}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 hover:bg-transparent"
                        onClick={() => handleSort(column)}
                      >
                        <span className="font-medium text-gray-900 text-sm">
                          {column}
                        </span>
                        {getSortIcon(column)}
                      </Button>
                    </div>
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-gray-500 font-mono">
                        {columnTypes[column].toUpperCase()}
                      </span>
                      {column === primaryKey && (
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          PK
                        </Badge>
                      )}
                    </div>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => (
              <tr 
                key={rowIndex}
                className={`
                  border-b border-gray-100 hover:bg-gray-50/50 transition-colors
                  ${rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50/30'}
                  ${selectedRows.has(rowIndex) ? 'bg-blue-50' : ''}
                `}
              >
                <td className="p-3">
                  <input 
                    type="checkbox"
                    className="rounded border-gray-300"
                    checked={selectedRows.has(rowIndex)}
                    onChange={(e) => {
                      const newSelected = new Set(selectedRows)
                      if (e.target.checked) {
                        newSelected.add(rowIndex)
                      } else {
                        newSelected.delete(rowIndex)
                      }
                      setSelectedRows(newSelected)
                    }}
                  />
                </td>
                {data.columns.map((column) => (
                  <td 
                    key={column} 
                    className="px-3 py-2 border-r border-gray-100 last:border-r-0 min-w-[120px] max-w-[300px]"
                  >
                    {renderCell(row[column], columnTypes[column], String(row[column] || ''))}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Table Footer */}
      <div className="bg-gray-50 border-t px-4 py-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>
            {selectedRows.size > 0 && `${selectedRows.size} selected • `}
            {data.rows.length} of {data.totalRows} rows
          </span>
          <span>
            {data.columns.length} columns
          </span>
        </div>
      </div>
    </div>
  )
}