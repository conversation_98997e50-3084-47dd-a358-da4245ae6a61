'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ChevronLeft, ChevronRight, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'
import { buildTableQuery, TABLE_SCHEMA_CONFIG } from '@/lib/database'
import { SheetTableDark } from './sheet-table-dark'

interface DatabaseTableViewerProps {
  tableName: string
  displayName: string
  shouldLoad?: boolean
}

interface TableData {
  rows: Record<string, any>[]
  columns: string[]
  totalRows: number
}

export function DatabaseTableViewer({ tableName, displayName, shouldLoad = false }: DatabaseTableViewerProps) {
  const [data, setData] = useState<TableData | null>(null)
  const [loading, setLoading] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
  })
  const loadingRef = useRef(false)

  const fetchTableData = async () => {
    // Prevent duplicate requests
    if (loadingRef.current) return
    
    loadingRef.current = true
    setLoading(true)
    try {
      const offset = (pagination.page - 1) * pagination.limit
      const sql = buildTableQuery(tableName, pagination.limit, offset)
      const params = [pagination.limit, offset]

      const response = await fetch('/api/database/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sql, params }),
      })

      const result = await response.json()

      if (result.success && result.body && Array.isArray(result.body)) {
        // Extract body data from each response element
        const rows = result.body.map((item: any) => item.body).filter((body: any) => body !== null && body !== undefined)
        
        if (rows.length > 0) {
          const columns = Object.keys(rows[0])
          const totalRows = rows.length

          setData({
            rows,
            columns,
            totalRows,
          })
          setPagination(prev => ({ ...prev, total: totalRows }))
          setHasLoaded(true)
        } else {
          setData({
            rows: [],
            columns: [],
            totalRows: 0,
          })
          setHasLoaded(true)
        }
      } else {
        toast.error(result.error_msg || 'Failed to fetch table data')
      }
    } catch (error) {
      toast.error('An error occurred while fetching table data')
    } finally {
      setLoading(false)
      loadingRef.current = false
    }
  }


  useEffect(() => {
    if (shouldLoad) {
      if (!hasLoaded) {
        // Initial load
        fetchTableData()
      } else {
        // Pagination or limit changes
        if (pagination.page > 1 || pagination.limit !== 50) {
          fetchTableData()
        }
      }
    }
  }, [shouldLoad, tableName, pagination.page, pagination.limit, hasLoaded])

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleLimitChange = (newLimit: number) => {
    setPagination(prev => ({ ...prev, limit: newLimit, page: 1 }))
  }

  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) {
      return 'NULL'
    }
    if (typeof value === 'object') {
      return JSON.stringify(value)
    }
    if (typeof value === 'boolean') {
      return value ? 'true' : 'false'
    }
    if (typeof value === 'string' && value.length > 100) {
      return value.substring(0, 100) + '...'
    }
    return String(value)
  }

  const getCellComponent = (value: any) => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground italic">NULL</span>
    }
    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'true' : 'false'}
        </Badge>
      )
    }
    if (typeof value === 'object') {
      return (
        <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
          {JSON.stringify(value)}
        </span>
      )
    }
    return <span>{formatCellValue(value)}</span>
  }

  const totalPages = Math.ceil(pagination.total / pagination.limit)
  const startPage = Math.max(1, pagination.page - 2)
  const endPage = Math.min(totalPages, pagination.page + 2)
  const tableSchema = TABLE_SCHEMA_CONFIG[tableName]

  return (
    <SheetTableDark
      data={data}
      tableName={tableName}
      displayName={displayName}
      primaryKey={tableSchema?.primaryKey}
      shouldLoad={shouldLoad}
      loading={loading}
      hasLoaded={hasLoaded}
      onRefresh={fetchTableData}
      pagination={{
        page: pagination.page,
        limit: pagination.limit,
        total: pagination.total,
        onPageChange: handlePageChange,
        onLimitChange: handleLimitChange,
      }}
    />
  )
}