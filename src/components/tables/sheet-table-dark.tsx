'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Co<PERSON>, 
  ArrowUpDown, 
  ArrowUp, 
  ArrowDown,
  ChevronLeft,
  ChevronRight,
  Key,
  Type,
  Hash,
  Calendar,
  CheckCircle,
  Database,
  RefreshCw
} from 'lucide-react'
import { toast } from 'sonner'
import { formatTimestamp, TABLE_SCHEMA_CONFIG, type ColumnSchema } from '@/lib/database'

interface SheetTableProps {
  data: {
    rows: Record<string, any>[]
    columns: string[]
    totalRows: number
  } | null
  tableName: string
  primaryKey?: string
  onRefresh?: () => void
  displayName: string
  shouldLoad: boolean
  loading: boolean
  hasLoaded: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    onPageChange: (newPage: number) => void
    onLimitChange: (newLimit: number) => void
  }
}

// Get column schema from PostgreSQL definitions
const getColumnSchema = (tableName: string, columnName: string): ColumnSchema | null => {
  const tableConfig = TABLE_SCHEMA_CONFIG[tableName]
  if (!tableConfig) return null
  return tableConfig.columns[columnName] || null
}

// Detect data presentation type based on schema and value
const detectPresentationType = (value: any, columnName: string, schema: ColumnSchema | null): string => {
  if (!schema) return 'text'
  
  // Use schema type as base
  const schemaType = schema.type.toLowerCase()
  
  // Email detection from value
  if (typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
    return 'email'
  }
  
  // Phone detection from value
  if (typeof value === 'string' && /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/[\s\-\(\)]/g, ''))) {
    return 'phone'
  }
  
  // URL detection from value
  if (typeof value === 'string' && /^https?:\/\//.test(value)) {
    return 'url'
  }
  
  // Map PostgreSQL types to presentation types
  if (schemaType.includes('uuid')) return 'uuid'
  if (schemaType.includes('timestamp') || schemaType.includes('time')) return 'timestamp'
  if (schemaType.includes('boolean')) return 'boolean'
  if (schemaType.includes('json')) return 'jsonb'
  if (schemaType.includes('integer') || schemaType.includes('serial') || schemaType.includes('real')) return 'number'
  if (schemaType.includes('vector')) return 'vector'
  
  return 'text'
}

const getColumnIcon = (schema: ColumnSchema | null, isPrimary?: boolean) => {
  if (isPrimary) return <Key className="h-3 w-3 text-amber-500" />
  
  if (!schema) return <Type className="h-3 w-3 text-muted-foreground" />
  
  const schemaType = schema.type.toLowerCase()
  
  if (schemaType.includes('uuid')) return <Hash className="h-3 w-3 text-blue-500" />
  if (schemaType.includes('timestamp') || schemaType.includes('time')) return <Calendar className="h-3 w-3 text-purple-500" />
  if (schemaType.includes('integer') || schemaType.includes('serial') || schemaType.includes('real')) return <Hash className="h-3 w-3 text-green-500" />
  if (schemaType.includes('boolean')) return <CheckCircle className="h-3 w-3 text-orange-500" />
  if (schemaType.includes('json')) return <Database className="h-3 w-3 text-indigo-500" />
  if (schemaType.includes('vector')) return <Database className="h-3 w-3 text-pink-500" />
  
  return <Type className="h-3 w-3 text-muted-foreground" />
}

const copyToClipboard = (text: string) => {
  navigator.clipboard.writeText(text)
  toast.success('Copied to clipboard')
}

export function SheetTableDark({
  data,
  tableName,
  displayName,
  primaryKey,
  onRefresh,
  shouldLoad,
  loading,
  hasLoaded,
  pagination,
}: SheetTableProps) {
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'asc' | 'desc'
  } | null>(null)
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set())

  // Get column schemas and presentation types
  const columnInfo = data?.columns.reduce((acc, column) => {
    const schema = getColumnSchema(tableName, column)
    const sampleValue = data?.rows.find(row => row[column] !== null && row[column] !== undefined)?.[column]
    const presentationType = detectPresentationType(sampleValue, column, schema)
    acc[column] = { schema, presentationType }
    return acc
  }, {} as Record<string, { schema: ColumnSchema | null, presentationType: string }>)

  const formatCellValue = (value: any): string => {
    if (value === null || value === undefined) return 'NULL'
    
    if (typeof value === 'object') {
      return JSON.stringify(value)
    }
    
    if (typeof value === 'boolean') {
      return value ? 'true' : 'false'
    }
    
    const str = String(value)
    return str.length > 30 ? str.substring(0, 30) + '...' : str
  }

  const renderCell = (value: any) => {
    return (
      <span 
        className="text-white text-sm cursor-pointer truncate block"
        onClick={() => copyToClipboard(String(value || ''))}
        title={String(value || '')}
      >
        {formatCellValue(value)}
      </span>
    )
  }

  const handleSort = (columnKey: string) => {
    let direction: 'asc' | 'desc' = 'asc'
    if (sortConfig && sortConfig.key === columnKey && sortConfig.direction === 'asc') {
      direction = 'desc'
    }
    setSortConfig({ key: columnKey, direction })
  }

  const getSortIcon = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return ''
    }
    return sortConfig.direction === 'asc' ? ' ↑' : ' ↓'
  }

  return (
    <div className="rounded-md border">
      {/* Header Section */}
      <div className="flex items-center justify-between p-4 border-b">
        <h3 className="text-lg font-medium">{displayName}</h3>
        <Button onClick={onRefresh} disabled={loading} size="sm">
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? 'Loading...' : 'Refresh'}
        </Button>
      </div>

      {/* Main Content Area (Table or Messages) */}
      <div className="">
        {!shouldLoad || (loading && !hasLoaded) ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>Loading data...</p>
          </div>
        ) : !data || data.rows.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No data found in {tableName} table
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/30 sticky top-0 z-10">
                <tr>
                  <th className="w-12 p-3 text-left">
                    <input 
                      type="checkbox" 
                      className="rounded border-input"
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedRows(new Set(data.rows.map((_, i) => i)))
                        } else {
                          setSelectedRows(new Set())
                        }
                      }}
                    />
                  </th>
                  {data.columns.map((column) => (
                    <th 
                      key={column} 
                      className="px-3 py-4 text-left border-r border-border last:border-r-0 w-32"
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 hover:bg-transparent text-white"
                        onClick={() => handleSort(column)}
                      >
                        <span className="font-medium text-white text-sm">
                          {column}{getSortIcon(column)}
                        </span>
                      </Button>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {data.rows.map((row, rowIndex) => (
                  <tr 
                    key={rowIndex}
                    className={`
                      border-b border-border hover:bg-muted/30 transition-colors
                      ${rowIndex % 2 === 0 ? 'bg-card' : 'bg-muted/10'}
                      ${selectedRows.has(rowIndex) ? 'bg-accent/50' : ''}
                    `}
                  >
                    <td className="p-3">
                      <input 
                        type="checkbox"
                        className="rounded border-input"
                        checked={selectedRows.has(rowIndex)}
                        onChange={(e) => {
                          const newSelected = new Set(selectedRows)
                          if (e.target.checked) {
                            newSelected.add(rowIndex)
                          } else {
                            newSelected.delete(rowIndex)
                          }
                          setSelectedRows(newSelected)
                        }}
                      />
                    </td>
                    {data.columns.map((column) => (
                      <td 
                        key={column} 
                        className="px-3 py-2 border-r border-border/50 last:border-r-0 w-32 h-10"
                      >
                        {renderCell(row[column])}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Enhanced Pagination */}
      {data && data.rows.length > 0 && pagination && (
        <div className="flex flex-col sm:flex-row sm:items-center justify-between bg-card border-t p-4 gap-2">
          {/* Left section: Row Count (e.g., "2 of 2 rows") */}
          <div className="flex items-center flex-1 justify-start">
            <span className="text-sm text-muted-foreground whitespace-nowrap">
              {pagination.total} of {pagination.total} rows
            </span>
          </div>

          {/* Middle section: Rows per page dropdown */}
          <div className="flex items-center justify-center flex-1">
            <Select
              value={pagination.limit.toString()}
              onValueChange={(value) => pagination.onLimitChange(parseInt(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="200">200</SelectItem>
                <SelectItem value="500">500</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Right section: Navigation Buttons (Previous/Next) */}
          <div className="flex items-center gap-2 flex-1 justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
            >
              Previous
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => pagination.onPageChange(pagination.page + 1)}
              disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}