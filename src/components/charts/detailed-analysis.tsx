'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { AnalysisTable } from './analysis-table'
import { DetailedAnalytics } from '@/types'
import { Settings, Monitor, Users, Bot } from 'lucide-react'

interface DetailedAnalysisSectionProps {
  data: DetailedAnalytics | null
  loading: boolean
}

export function DetailedAnalysisSection({ data, loading }: DetailedAnalysisSectionProps) {
  const analysisViews = [
    {
      id: 'operation',
      label: 'Operation',
      icon: Settings,
      title: 'Usage by Operation',
      description: 'Breakdown of requests, tokens, and costs by operation type',
      data: data?.operations || [],
      nameLabel: 'Operation',
    },
    {
      id: 'platform',
      label: 'Platform',
      icon: Monitor,
      title: 'Usage by Platform',
      description: 'Breakdown of requests, tokens, and costs by platform',
      data: data?.platforms || [],
      nameLabel: 'Platform',
    },
    {
      id: 'client',
      label: 'Client',
      icon: Users,
      title: 'Usage by Client',
      description: 'Breakdown of requests, tokens, and costs by client',
      data: data?.clients || [],
      nameLabel: 'Client',
    },
    {
      id: 'model',
      label: 'Model',
      icon: Bot,
      title: 'Usage by Model',
      description: 'Breakdown of requests, tokens, and costs by model',
      data: data?.models || [],
      nameLabel: 'Model',
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Detailed Analysis</CardTitle>
        <CardDescription>
          Comprehensive breakdown of usage patterns across different dimensions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="operation" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            {analysisViews.map((view) => (
              <TabsTrigger 
                key={view.id} 
                value={view.id} 
                className="flex items-center gap-2"
              >
                <view.icon className="h-4 w-4" />
                {view.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {analysisViews.map((view) => (
            <TabsContent key={view.id} value={view.id} className="space-y-4">
              <div>
                <h3 className="text-lg font-medium">{view.title}</h3>
                <p className="text-sm text-muted-foreground">{view.description}</p>
              </div>
              <AnalysisTable
                data={view.data}
                loading={loading}
                title={view.title}
                nameLabel={view.nameLabel}
              />
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}