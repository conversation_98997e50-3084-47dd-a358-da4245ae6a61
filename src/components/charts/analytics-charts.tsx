'use client'

import {
  BarChart,
  Bar,
  LineChart,
  Line,
  Area,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts'

interface AnalyticsChartsProps {
  data: Array<{ date: string; count?: number; cost?: number }>
  type: 'requests' | 'costs'
  loading: boolean
}

const CustomTooltip = ({ active, payload, type }: any) => {
  if (active && payload && payload.length) {
    const value = payload[0].value
    const formattedValue = type === 'costs' 
      ? new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 4,
        }).format(value)
      : new Intl.NumberFormat('en-US').format(value)
    
    return (
      <div style={{ 
        backgroundColor: 'hsl(var(--card))',
        border: '1px solid hsl(var(--border))',
        borderRadius: '6px',
        padding: '8px',
        color: 'white',
        fontSize: '14px',
        fontWeight: '500'
      }}>
        {formattedValue}
      </div>
    )
  }
  return null
}

export function AnalyticsCharts({ data, type, loading }: AnalyticsChartsProps) {
  if (loading) {
    return (
      <div className="h-[300px] w-full bg-muted animate-pulse rounded" />
    )
  }

  if (!data || data.length === 0) {
    return (
      <div className="h-[300px] flex items-center justify-center text-muted-foreground">
        No data available for the selected period
      </div>
    )
  }

  const formatTooltipValue = (value: number) => {
    if (type === 'costs') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 4,
      }).format(value)
    }
    return new Intl.NumberFormat('en-US').format(value)
  }

  const formatXAxisLabel = (tickItem: string) => {
    const date = new Date(tickItem)
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  }

  const dataKey = type === 'requests' ? 'count' : 'cost'
  const color = type === 'requests' ? '#3b82f6' : '#10b981'

  if (type === 'costs') {
    // Use line chart for costs
    return (
      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart
            data={data}
            margin={{
              top: 5,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="date"
              tickFormatter={formatXAxisLabel}
              className="text-xs"
            />
            <YAxis className="text-xs" />
            <Tooltip
              content={<CustomTooltip type={type} />}
              cursor={{ stroke: 'none' }}
            />
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              fill={color}
              fillOpacity={0.3}
              dot={{ fill: color, r: 4 }}
              activeDot={{ r: 6, fill: color }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    )
  }

  // Use bar chart for requests
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis
            dataKey="date"
            tickFormatter={formatXAxisLabel}
            className="text-xs"
          />
          <YAxis className="text-xs" />
          <Tooltip
            content={<CustomTooltip type={type} />}
            cursor={false}
          />
          <Bar
            dataKey={dataKey}
            fill={color}
            radius={[2, 2, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}