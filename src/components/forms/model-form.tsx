'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ModelData } from '@/types'
import { Trash2 } from 'lucide-react'

interface ModelFormProps {
  onSubmit: (data: Omit<ModelData, 'id' | 'updated_at'>) => void
  loading?: boolean
  initialData?: Partial<ModelData>
  onSuccess?: () => void
  onDelete?: (modelId: number) => void
  deleteLoading?: boolean
}

export function ModelForm({ onSubmit, loading = false, initialData, onSuccess, onDelete, deleteLoading = false }: ModelFormProps) {
  // Helper functions for pricing conversion
  const toDisplayValue = (apiValue: number) => (apiValue * 1000000).toFixed(2)
  const toApiValue = (displayValue: string) => Number(displayValue) / 1000000

  const [showUpdateConfirm, setShowUpdateConfirm] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [pendingFormData, setPendingFormData] = useState<any>(null)

  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    model: initialData?.model || '',
    provider: initialData?.provider || '',
    key: initialData?.key || '',
    input_cost_per_token: initialData?.input_cost_per_token ? toDisplayValue(initialData.input_cost_per_token) : '0.00',
    output_cost_per_token: initialData?.output_cost_per_token ? toDisplayValue(initialData.output_cost_per_token) : '0.00',
    cache_read_cost_per_token: initialData?.cache_read_cost_per_token ? toDisplayValue(initialData.cache_read_cost_per_token) : '0.00',
    cache_write_cost_per_token: initialData?.cache_write_cost_per_token ? toDisplayValue(initialData.cache_write_cost_per_token) : '0.00',
    thinking_cost_per_token: initialData?.thinking_cost_per_token ? toDisplayValue(initialData.thinking_cost_per_token) : '0.00',
    audio_input_cost_per_token: initialData?.audio_input_cost_per_token ? toDisplayValue(initialData.audio_input_cost_per_token) : '0.00',
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Convert display values back to API values
    const apiData = {
      name: formData.name,
      model: formData.model,
      provider: formData.provider,
      key: formData.key,
      input_cost_per_token: toApiValue(formData.input_cost_per_token),
      output_cost_per_token: toApiValue(formData.output_cost_per_token),
      cache_read_cost_per_token: toApiValue(formData.cache_read_cost_per_token),
      cache_write_cost_per_token: toApiValue(formData.cache_write_cost_per_token),
      thinking_cost_per_token: toApiValue(formData.thinking_cost_per_token),
      audio_input_cost_per_token: toApiValue(formData.audio_input_cost_per_token),
    }
    
    if (initialData) {
      // Show confirmation for updates
      setPendingFormData(apiData)
      setShowUpdateConfirm(true)
    } else {
      // Direct submit for new models
      onSubmit(apiData)
    }
  }

  const handleConfirmUpdate = () => {
    if (pendingFormData) {
      onSubmit(pendingFormData)
      setShowUpdateConfirm(false)
      setPendingFormData(null)
    }
  }

  const handleConfirmDelete = () => {
    if (onDelete && initialData?.id) {
      onDelete(initialData.id)
      setShowDeleteConfirm(false)
    }
  }

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const resetForm = () => {
    setFormData({
      name: '',
      model: '',
      provider: '',
      key: '',
      input_cost_per_token: '0.00',
      output_cost_per_token: '0.00',
      cache_read_cost_per_token: '0.00',
      cache_write_cost_per_token: '0.00',
      thinking_cost_per_token: '0.00',
      audio_input_cost_per_token: '0.00',
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Basic Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="name">Display Name *</Label>
            <Input
              id="name"
              placeholder="e.g., Gemini Flash Small"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="model">Model ID *</Label>
            <Input
              id="model"
              placeholder="e.g., gemini-2.5-flash-lite-preview-06-17"
              value={formData.model}
              onChange={(e) => handleInputChange('model', e.target.value)}
              required
              disabled={loading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="provider">Provider *</Label>
            <Input
              id="provider"
              placeholder="e.g., gemini, openai, anthropic"
              value={formData.provider}
              onChange={(e) => handleInputChange('provider', e.target.value)}
              required
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="key">API Key *</Label>
            <Input
              id="key"
              placeholder="API key or identifier"
              value={formData.key}
              onChange={(e) => handleInputChange('key', e.target.value)}
              required
              disabled={loading}
            />
          </div>
        </div>
      </div>

      {/* Pricing Configuration */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Pricing Configuration</h3>
        <p className="text-sm text-muted-foreground">
          Configure cost per million tokens for different types of operations. Use decimal values (e.g., 0.10 for $0.10/1M tokens)
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="input_cost">Input Cost</Label>
            <Input
              id="input_cost"
              type="number"
              step="0.01"
              placeholder="0.10"
              value={formData.input_cost_per_token}
              onChange={(e) => handleInputChange('input_cost_per_token', e.target.value)}
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="output_cost">Output Cost</Label>
            <Input
              id="output_cost"
              type="number"
              step="0.01"
              placeholder="0.40"
              value={formData.output_cost_per_token}
              onChange={(e) => handleInputChange('output_cost_per_token', e.target.value)}
              disabled={loading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="cache_read_cost">Cache Read Cost</Label>
            <Input
              id="cache_read_cost"
              type="number"
              step="0.01"
              placeholder="0.025"
              value={formData.cache_read_cost_per_token}
              onChange={(e) => handleInputChange('cache_read_cost_per_token', e.target.value)}
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="cache_write_cost">Cache Write Cost</Label>
            <Input
              id="cache_write_cost"
              type="number"
              step="0.01"
              placeholder="0.50"
              value={formData.cache_write_cost_per_token}
              onChange={(e) => handleInputChange('cache_write_cost_per_token', e.target.value)}
              disabled={loading}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="thinking_cost">Thinking Cost</Label>
            <Input
              id="thinking_cost"
              type="number"
              step="0.01"
              placeholder="0.00"
              value={formData.thinking_cost_per_token}
              onChange={(e) => handleInputChange('thinking_cost_per_token', e.target.value)}
              disabled={loading}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="audio_input_cost">Audio Input Cost</Label>
            <Input
              id="audio_input_cost"
              type="number"
              step="0.01"
              placeholder="0.50"
              value={formData.audio_input_cost_per_token}
              onChange={(e) => handleInputChange('audio_input_cost_per_token', e.target.value)}
              disabled={loading}
            />
          </div>
        </div>
      </div>

      <div className="flex gap-3 pt-4">
        <Button type="submit" disabled={loading} className="flex-1">
          {loading ? 'Saving...' : initialData ? 'Update Model' : 'Add Model'}
        </Button>
        {initialData && onDelete && (
          <Button 
            type="button" 
            variant="destructive" 
            onClick={() => setShowDeleteConfirm(true)}
            disabled={deleteLoading}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        )}
      </div>

      {/* Update Confirmation Dialog */}
      <Dialog open={showUpdateConfirm} onOpenChange={setShowUpdateConfirm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Update</DialogTitle>
            <DialogDescription>
              Are you sure you want to update the model "{initialData?.name}"? This action will modify the model configuration.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowUpdateConfirm(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirmUpdate} disabled={loading}>
              {loading ? 'Updating...' : 'Update Model'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the model "{initialData?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete} disabled={deleteLoading}>
              {deleteLoading ? 'Deleting...' : 'Delete Model'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </form>
  )
}