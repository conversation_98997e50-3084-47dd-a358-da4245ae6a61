'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ModelsTable } from '@/components/tables/models-table'
import { ModelForm } from '@/components/forms/model-form'
import { toast } from 'sonner'
import { ModelData } from '@/types'
import { RefreshCw, List, Plus } from 'lucide-react'

export default function ModelsPage() {
  const [models, setModels] = useState<ModelData[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState('list')
  const [resetFormTrigger, setResetFormTrigger] = useState(0)

  const fetchModels = async () => {
    if (refreshing) return // Prevent duplicate calls
    setRefreshing(true)
    try {
      const response = await fetch('/api/models')
      const result = await response.json()

      if (result.success) {
        setModels(result.data || [])
      } else {
        toast.error(result.error || 'Failed to fetch models')
      }
    } catch {
      toast.error('An error occurred while fetching models')
    } finally {
      setRefreshing(false)
    }
  }

  useEffect(() => {
    fetchModels()
  }, [])

  const handleAddModel = async (modelData: Omit<ModelData, 'id' | 'updated_at'>) => {
    setLoading(true)
    try {
      const response = await fetch('/api/models', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(modelData),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Model added successfully!')
        // Stay on add tab, refresh models list, and reset form
        fetchModels()
        setResetFormTrigger(prev => prev + 1) // Trigger form reset
      } else {
        toast.error(result.error || 'Failed to add model')
      }
    } catch {
      toast.error('An error occurred while adding the model')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteModel = async (modelId: number) => {
    try {
      const response = await fetch('/api/models', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: modelId }),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Model deleted successfully!')
        fetchModels()
      } else {
        toast.error(result.error || 'Failed to delete model')
      }
    } catch {
      toast.error('An error occurred while deleting the model')
    }
  }

  const handleUpdateModel = async (modelId: number, modelData: Partial<ModelData>) => {
    try {
      const response = await fetch('/api/models', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: modelId, ...modelData }),
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Model updated successfully!')
        fetchModels()
      } else {
        toast.error(result.error || 'Failed to update model')
      }
    } catch {
      toast.error('An error occurred while updating the model')
    }
  }

  const filteredModels = models
    .filter(model =>
      model.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
      model.provider.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Model Management</h1>
          <p className="text-muted-foreground">
            Manage LLM models and their pricing configurations
          </p>
        </div>
        <Button
          variant="outline"
          onClick={fetchModels}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list" className="flex items-center gap-2">
            <List className="h-4 w-4" />
            Models
          </TabsTrigger>
          <TabsTrigger value="add" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Model
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Models</CardTitle>
              <CardDescription>
                Current LLM models with pricing information
              </CardDescription>
              <div className="flex gap-4 items-center">
                <div className="flex-1">
                  <Label htmlFor="search">Search Models</Label>
                  <Input
                    id="search"
                    placeholder="Search by name, model, or provider..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="text-sm text-muted-foreground">
                  {filteredModels.length} of {models.length} models
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <ModelsTable
                models={filteredModels}
                onDelete={handleDeleteModel}
                onUpdate={handleUpdateModel}
                loading={refreshing}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="add" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Add New Model</CardTitle>
              <CardDescription>
                Configure a new LLM model with pricing information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ModelForm 
                onSubmit={handleAddModel} 
                loading={loading}
                key={resetFormTrigger} // Reset form when key changes
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}