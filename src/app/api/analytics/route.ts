import { NextRequest, NextResponse } from 'next/server'
import { analyticsWebhooks, webhookCache } from '@/lib/webhooks'
import { RequestLogData } from '@/types'

// Helper function to process raw logs into analytics data
function processLogsToAnalytics(logs: RequestLogData[]) {
  if (!logs || logs.length === 0) {
    return {
      total_requests: 0,
      total_cost: 0,
      total_tokens: 0,
      average_cost_per_request: 0,
      daily_requests: [],
      daily_costs: [],
      recent_logs: []
    }
  }

  // Calculate totals
  let totalCost = 0
  let totalTokens = 0
  const dailyData = new Map<string, { requests: number; cost: number }>()

  logs.forEach((log, index) => {
    // Convert string values to numbers and handle NaN
    const logCost = Number(log.total_cost) || 0
    const inputTokens = Number(log.input_tokens) || 0
    const outputTokens = Number(log.output_tokens) || 0
    const cacheReadTokens = Number(log.cache_read_tokens) || 0
    const cacheWriteTokens = Number(log.cache_write_tokens) || 0
    const thinkingTokens = Number(log.thinking_tokens) || 0
    const audioInputTokens = Number(log.audio_input_tokens) || 0

    // Sum up costs
    totalCost += logCost

    // Sum up all token types (excluding cache read tokens as they're already counted in input tokens)
    const logTokens = inputTokens + outputTokens + 
                     cacheWriteTokens + thinkingTokens + audioInputTokens
    totalTokens += logTokens

    // Group by date for daily metrics
    const date = log.created_at.split('T')[0] // Extract YYYY-MM-DD
    const dayData = dailyData.get(date) || { requests: 0, cost: 0 }
    dayData.requests += 1
    dayData.cost += logCost
    dailyData.set(date, dayData)
  })

  // Convert daily data to arrays and sort by date
  const daily_requests = Array.from(dailyData.entries())
    .map(([date, data]) => ({ date, count: data.requests }))
    .sort((a, b) => a.date.localeCompare(b.date))

  const daily_costs = Array.from(dailyData.entries())
    .map(([date, data]) => ({ date, cost: data.cost }))
    .sort((a, b) => a.date.localeCompare(b.date))

  return {
    total_requests: logs.length,
    total_cost: totalCost,
    total_tokens: totalTokens,
    average_cost_per_request: logs.length > 0 ? totalCost / logs.length : 0,
    daily_requests,
    daily_costs,
    recent_logs: logs.slice(-100) // Keep last 100 logs for detailed analysis
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    // Create cache key based on date range
    const cacheKey = `analytics_processed_${startDate}_${endDate}`
    
    // Check cache first
    const cachedData = webhookCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        data: cachedData,
        cached: true,
      })
    }

    // Get raw logs from webhook
    const result = await analyticsWebhooks.fetchOverview(
      startDate || undefined,
      endDate || undefined
    )

    if (!result.success) {
      console.error('Analytics API: Webhook failed:', result.error)
      return NextResponse.json({
        success: false,
        error: result.error || 'Failed to fetch analytics data'
      })
    }

    // The webhook returns an array of logs, not an object
    const rawLogs = Array.isArray(result.data) ? result.data : []

    // Process raw logs into analytics data
    const analyticsData = processLogsToAnalytics(rawLogs)

    // Cache the processed result for 5 minutes
    webhookCache.set(cacheKey, analyticsData, 5)

    return NextResponse.json({
      success: true,
      data: analyticsData
    })
  } catch (error) {
    console.error('Analytics API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}