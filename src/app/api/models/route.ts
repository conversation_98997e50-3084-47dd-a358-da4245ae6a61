import { NextRequest, NextResponse } from 'next/server'
import { modelWebhooks } from '@/lib/webhooks'

export async function GET() {
  try {
    const result = await modelWebhooks.fetchModels()
    return NextResponse.json(result)
  } catch (error) {
    console.error('Models GET API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const modelData = await request.json()
    
    console.log('Adding model:', {
      name: modelData.name,
      model: modelData.model,
      provider: modelData.provider,
      key: modelData.key ? 'sk-***' : 'missing'
    })

    // Validate required fields
    const requiredFields = ['name', 'model', 'provider', 'key']
    for (const field of requiredFields) {
      if (!modelData[field]) {
        return NextResponse.json(
          { success: false, error: `${field} is required` },
          { status: 400 }
        )
      }
    }

    // Ensure pricing fields are numbers
    const numericFields = [
      'input_cost_per_token',
      'output_cost_per_token', 
      'cache_read_cost_per_token',
      'cache_write_cost_per_token',
      'thinking_cost_per_token',
      'audio_input_cost_per_token'
    ]
    
    for (const field of numericFields) {
      if (modelData[field] !== undefined && modelData[field] !== null) {
        modelData[field] = Number(modelData[field]) || 0
      } else {
        modelData[field] = 0
      }
    }

    const result = await modelWebhooks.addModel(modelData)
    
    console.log('Webhook result:', {
      success: result.success,
      error: result.error
    })
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Models POST API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, ...modelData } = await request.json()
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required for update' },
        { status: 400 }
      )
    }
    
    console.log('Updating model:', {
      id,
      updatedFields: Object.keys(modelData)
    })

    // Ensure pricing fields are numbers
    const numericFields = [
      'input_cost_per_token',
      'output_cost_per_token', 
      'cache_read_cost_per_token',
      'cache_write_cost_per_token',
      'thinking_cost_per_token',
      'audio_input_cost_per_token'
    ]
    
    for (const field of numericFields) {
      if (modelData[field] !== undefined && modelData[field] !== null) {
        modelData[field] = Number(modelData[field]) || 0
      }
    }

    const result = await modelWebhooks.updateModel(id, modelData)
    
    console.log('Update result:', {
      success: result.success,
      error: result.error
    })
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Models PUT API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json()
    
    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required for deletion' },
        { status: 400 }
      )
    }
    
    console.log('Deleting model:', { id })

    const result = await modelWebhooks.deleteModel(id)
    
    console.log('Delete result:', {
      success: result.success,
      error: result.error
    })
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Models DELETE API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}