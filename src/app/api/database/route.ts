import { NextRequest, NextResponse } from 'next/server'
import { createBearerToken } from '@/lib/jwt'

export async function POST(request: NextRequest) {
  try {
    const { sql, params } = await request.json()

    if (!sql) {
      return NextResponse.json(
        { success: false, error_msg: 'SQL query is required' },
        { status: 400 }
      )
    }

    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    const webhookToken = process.env.CHHLAT_DB_WEBHOOK_TOKEN

    if (!webhookUrl || !webhookToken) {
      return NextResponse.json(
        { success: false, error_msg: 'Database webhook configuration is missing' },
        { status: 500 }
      )
    }

    const bearerToken = createBearerToken()
    
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Authorization': bearerToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql,
        params: params || [],
      }),
    })

    const result = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false, 
          error_msg: result.error_msg || 'Database query failed',
          body: null 
        },
        { status: response.status }
      )
    }

    return NextResponse.json({
      success: result.success || true,
      body: result.body || result,
      error_msg: result.error_msg || null,
    })
  } catch (error) {
    console.error('Database API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error_msg: 'Internal server error',
        body: null 
      },
      { status: 500 }
    )
  }
}