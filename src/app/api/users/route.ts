import { NextRequest, NextResponse } from 'next/server'
import { userWebhooks } from '@/lib/webhooks'
import { updateUserPlan, addUserMessages } from '@/lib/supabase-admin'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, username, plan, billCycle, message_count } = body

    if (action === 'update_plan') {
      if (!username || !plan || !billCycle) {
        return NextResponse.json(
          { success: false, error: 'Username, plan, and bill cycle are required' },
          { status: 400 }
        )
      }

      // Step 1-5: Handle Supabase operations directly
      const supabaseResult = await updateUserPlan(username, plan, billCycle)

      if (!supabaseResult.success) {
        return NextResponse.json(
          { success: false, error: supabaseResult.error },
          { status: supabaseResult.error === 'No user found with that username' ? 404 : 500 }
        )
      }

      // Step 6: Send notification to webhook
      try {
        await userWebhooks.updatePlanNotification({
          mode: 'update_plan',
          client_id: supabaseResult.data!.client_id,
          plan: supabaseResult.data!.plan,
          usage_limit: supabaseResult.data!.usage_limit,
        })
      } catch (webhookError) {
        console.error('Webhook notification failed:', webhookError)
        // Don't fail the entire operation if webhook fails
      }

      // Step 7: Return success response with Cambodia time for admin UI
      const nextBillingDate = new Date(supabaseResult.data!.next_billing_date)
      const cambodiaTime = new Date(nextBillingDate.getTime() + (7 * 60 * 60 * 1000))
      
      return NextResponse.json({
        success: true,
        data: {
          username: supabaseResult.data!.username,
          plan: supabaseResult.data!.plan,
          next_billing_date: cambodiaTime.toISOString().replace('Z', '+07:00'),
        }
      })
    }

    if (action === 'add_message') {
      if (!username || !message_count) {
        return NextResponse.json(
          { success: false, error: 'Username and message count are required' },
          { status: 400 }
        )
      }

      // Step 1-2: Handle Supabase operations directly
      const supabaseResult = await addUserMessages(username, message_count)

      if (!supabaseResult.success) {
        return NextResponse.json(
          { success: false, error: supabaseResult.error },
          { status: supabaseResult.error === 'No user found with that username' ? 404 : 500 }
        )
      }

      // Step 3: Send notification to webhook
      try {
        await userWebhooks.addMessageNotification({
          mode: 'add_message',
          client_id: supabaseResult.data!.client_id,
          usage_limit: supabaseResult.data!.new_usage_limit,
        })
      } catch (webhookError) {
        console.error('Webhook notification failed:', webhookError)
        // Don't fail the entire operation if webhook fails
      }

      // Step 4: Return success response
      return NextResponse.json({
        success: true,
        data: {
          username: supabaseResult.data!.username,
          previous_usage_limit: supabaseResult.data!.previous_usage_limit,
          new_usage_limit: supabaseResult.data!.new_usage_limit,
          messages_added: supabaseResult.data!.messages_added,
        }
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('User API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

