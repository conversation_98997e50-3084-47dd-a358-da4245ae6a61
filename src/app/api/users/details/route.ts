import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const username = searchParams.get('username')

    if (!username) {
      return NextResponse.json(
        { success: false, error: 'Username is required' },
        { status: 400 }
      )
    }

    const supabase = createServiceRoleClient()

    // Find client by username - select all fields
    const { data: client, error } = await supabase
      .from('clients')
      .select('*')
      .eq('username', username)
      .single()

    if (error) {
      console.error('Error fetching client:', error)
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: client
    })

  } catch (error) {
    console.error('Error in GET /api/users/details:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      username, 
      company_name,
      contact_email,
      contact_phone,
      status,
      sector,
      lang,
      lang_2,
      usage_used,
      usage_limit,
      contact_need
    } = body

    if (!username) {
      return NextResponse.json(
        { success: false, error: 'Username is required' },
        { status: 400 }
      )
    }

    const supabase = createServiceRoleClient()

    // First, find the current client data
    const { data: currentClient, error: findError } = await supabase
      .from('clients')
      .select('*')
      .eq('username', username)
      .single()

    if (findError) {
      console.error('Error finding client:', findError)
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Prepare update data with only the editable fields
    const updateData: Record<string, any> = {
      updated_at: new Date(Date.now() + (7 * 60 * 60 * 1000)).toISOString()
    }

    // Only update fields that are provided and editable
    if (company_name !== undefined) updateData.company_name = company_name
    if (contact_email !== undefined) updateData.contact_email = contact_email
    if (contact_phone !== undefined) updateData.contact_phone = contact_phone
    if (status !== undefined) updateData.status = status
    if (sector !== undefined) updateData.sector = sector
    if (lang !== undefined) updateData.lang = lang
    if (lang_2 !== undefined) updateData.lang_2 = lang_2
    if (usage_used !== undefined) updateData.usage_used = usage_used
    if (usage_limit !== undefined) updateData.usage_limit = usage_limit
    if (contact_need !== undefined) updateData.contact_need = contact_need

    // Update the client record
    const { data: updatedClient, error: updateError } = await supabase
      .from('clients')
      .update(updateData)
      .eq('username', username)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating client:', updateError)
      return NextResponse.json(
        { success: false, error: 'Failed to update user details' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: updatedClient
    })

  } catch (error) {
    console.error('Error in PUT /api/users/details:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}