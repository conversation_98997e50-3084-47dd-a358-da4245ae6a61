import { NextRequest, NextResponse } from 'next/server'
import { userWebhooks } from '@/lib/webhooks'
import { UserActionData } from '@/types'

export async function POST(request: NextRequest) {
  try {
    const body: UserActionData = await request.json()
    const { action, user_id, email, plan, message_count, additional_data } = body

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Action is required' },
        { status: 400 }
      )
    }

    // Validate required fields based on action
    switch (action) {
      case 'upgrade_plan':
        if (!plan || (!user_id && !email)) {
          return NextResponse.json(
            { success: false, error: 'Plan and either user_id or email are required' },
            { status: 400 }
          )
        }
        break
      case 'add_messages':
        if (!message_count || (!user_id && !email)) {
          return NextResponse.json(
            { success: false, error: 'Message count and either user_id or email are required' },
            { status: 400 }
          )
        }
        break
      case 'reset_password':
      case 'clear_data':
        if (!user_id && !email) {
          return NextResponse.json(
            { success: false, error: 'Either user_id or email is required' },
            { status: 400 }
          )
        }
        break
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }

    const result = await userWebhooks.triggerUserAction({
      action,
      user_id,
      email,
      plan,
      message_count,
      additional_data,
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('User action API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}