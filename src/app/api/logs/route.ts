import { NextRequest, NextResponse } from 'next/server'
import { analyticsWebhooks, webhookCache } from '@/lib/webhooks'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const rows = parseInt(searchParams.get('rows') || '50')
    
    // Extract filter parameters
    const filters: Record<string, any> = {}
    const filterKeys = [
      'start_date', 'end_date', 'filter_data', 'mode', 'type', 'filter_type', 'task_value'
    ]
    
    filterKeys.forEach(key => {
      const value = searchParams.get(key)
      if (value) {
        filters[key] = value
      }
    })

    // Create cache key
    const cacheKey = `logs_${page}_${rows}_${JSON.stringify(filters)}`
    
    // Check cache first (shorter cache time for logs)
    const cachedData = webhookCache.get(cacheKey)
    if (cachedData) {
      return NextResponse.json({
        success: true,
        data: cachedData,
        cached: true,
      })
    }

    const result = await analyticsWebhooks.fetchRequestLogs(page, rows, filters)

    if (result.success && result.data) {
      // Process the webhook response - it returns direct array of logs
      let processedData
      
      if (Array.isArray(result.data)) {
        // Webhook returns direct array, wrap it with pagination metadata
        processedData = {
          data: result.data,
          page: page,
          total: result.data.length,
          total_pages: result.data.length < rows ? page : page + 1, // Estimate if there are more pages
          limit: rows
        }
      } else {
        // Webhook returns structured response (fallback)
        processedData = result.data
      }
      
      // Cache the processed result for 2 minutes
      webhookCache.set(cacheKey, processedData, 2)
      
      return NextResponse.json({
        success: true,
        data: processedData
      })
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Logs API error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}