# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server
- `npm run build` - Build production application 
- `npm run start` - Start production server
- `npm run lint` - Run ESLint (currently disabled in builds)

## Architecture Overview

This is a Next.js 15.2.3 admin dashboard for LLM analytics and user management with a simplified routing structure:

### Data Sources
- **Supabase**: Primary authentication and user sessions
- **N8N Webhooks**: All external data (analytics, models, user actions) accessed via server-side webhook calls

### Authentication Flow
- `src/middleware.ts` provides global auth protection using Supabase SSR
- All routes redirect to `/login` if unauthenticated except auth routes
- Three Supabase client types in `src/lib/supabase.ts`:
  - `createClient()` - Client-side browser client
  - `createServerSupabaseClient()` - Server components (async cookies import)
  - `createMiddlewareSupabaseClient()` - Middleware with response handling

### Route Architecture
Uses Next.js App Router with simplified structure:
- `/login` - Authentication page with centered layout
- `/` - Main dashboard with sidebar layout
- `/analytics`, `/models`, `/users`, `/logs` - Direct routes with sidebar
- `api/n8n/` - Server-side webhook proxy routes
- `api/analytics/` - Analytics data processing routes

### N8N Integration Pattern
All external data flows through webhook utilities in `src/lib/webhooks.ts`:
- `modelWebhooks` - CRUD operations for LLM models
- `analyticsWebhooks` - Request logs and dashboard metrics 
- `userWebhooks` - User management actions
- `WebhookCache` class provides 2-5 minute caching to reduce API calls

### Component Structure
- `src/components/ui/` - shadcn/ui base components
- `src/components/layout/` - Admin sidebar with navigation and theme toggle
- `src/components/tables/` - Data tables with pagination and filtering
- `src/components/charts/` - Recharts analytics visualizations
- `src/components/forms/` - Form components for user/model management
- `src/components/providers/` - Context providers (Auth, Theme, Toast)

### State Management
- React Context for authentication (`AuthProvider`) and theme (`ThemeProvider`)
- Local component state with hooks
- Server state cached via webhook utilities
- No global state management library

### UI System
- Tailwind CSS v4 with CSS variables for theming
- shadcn/ui component system with custom theme variables
- Dark/light mode via `data-theme` attribute and CSS custom properties
- Toast notifications via Sonner

## Key Files

### Configuration
- `next.config.ts` - ESLint/TypeScript errors ignored for builds
- `.env.local` - Environment variables (Supabase keys + N8N webhook URLs)
- `src/types/index.ts` - Centralized TypeScript definitions

### Critical Utilities
- `src/lib/supabase.ts` - Authentication client factories
- `src/lib/webhooks.ts` - N8N integration with caching
- `src/middleware.ts` - Global auth protection

### Main Features
- `/analytics` - Dashboard with metrics cards and Recharts visualizations
- `/models` - LLM model CRUD with pricing configuration
- `/users` - User creation and problem resolution actions
- `/logs` - Paginated request logs with advanced filtering

## Development Patterns

### Adding New Features
1. Create page directory and `page.tsx` in `app/` folder
2. Add API routes in `api/n8n/` for webhook integration
3. Create reusable components in `components/`
4. Update types in `src/types/index.ts`

### Webhook Integration
All external data must go through server-side API routes that call N8N webhooks:
- Never call webhook URLs directly from client components
- Use the webhook utility functions in `src/lib/webhooks.ts`
- Implement caching for frequently accessed data
- Handle loading states and error cases

### Authentication
- Server components: Use `createServerSupabaseClient()`
- Client components: Use `createClient()` with `useAuth()` hook
- API routes: Use appropriate client based on context
- All auth state managed by `AuthProvider` context

### Styling
- Use existing shadcn/ui components where possible
- Follow Tailwind CSS v4 patterns with CSS variables
- Maintain theme consistency across light/dark modes
- Use design tokens from globals.css

## Environment Variables Required

```bash
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# N8N Webhooks (all server-side only)
MODEL_WEBHOOK_URL=
REQUEST_LOG_WEBHOOK_URL=  
USER_WEBHOOK_URL=
```

## Security Notes

- All webhook calls must be server-side only (API routes)
- Never expose webhook URLs to client-side code
- Validate all inputs before sending to webhooks
- Use service role key only for admin operations
- Middleware provides automatic auth protection